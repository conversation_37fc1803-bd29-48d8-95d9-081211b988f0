// QTS Brand Variables
// Primary brand colors for QTS
$qts-primary-color: #004d99;      // QTS primary blue
$qts-secondary-color: #003366;    // QTS darker blue for footer
$qts-accent-color: #0066cc;       // Lighter blue for accents
$qts-text-color: #ffffff;         // White text on dark backgrounds
$qts-text-dark: #333333;          // Dark text for light backgrounds

// Typography
$qts-font-family: 'Arial', sans-serif;
$qts-heading-font-weight: bold;
$qts-normal-font-weight: 400;
$qts-medium-font-weight: 500;

// Font sizes
$qts-font-size-small: 13px;
$qts-font-size-normal: 14px;
$qts-font-size-medium: 16px;
$qts-font-size-large: 18px;
$qts-font-size-xl: 20px;
$qts-font-size-xxl: 24px;

// Spacing
$qts-spacing-xs: 4px;
$qts-spacing-sm: 8px;
$qts-spacing-md: 12px;
$qts-spacing-lg: 16px;
$qts-spacing-xl: 20px;
$qts-spacing-xxl: 24px;
$qts-spacing-xxxl: 30px;

// Border radius
$qts-border-radius-sm: 3px;
$qts-border-radius-md: 4px;
$qts-border-radius-lg: 6px;

// Transitions
$qts-transition-fast: 0.2s ease;
$qts-transition-normal: 0.3s ease;
$qts-transition-slow: 0.4s ease;

// Breakpoints
$qts-breakpoint-mobile: 768px;
$qts-breakpoint-tablet: 1024px;
$qts-breakpoint-desktop: 1200px;

// Z-index layers
$qts-z-index-header: 1000;
$qts-z-index-navigation: 1100;
$qts-z-index-modal: 1200;
$qts-z-index-tooltip: 1300;

// Box shadows
$qts-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
$qts-shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.15);
$qts-shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.2);
