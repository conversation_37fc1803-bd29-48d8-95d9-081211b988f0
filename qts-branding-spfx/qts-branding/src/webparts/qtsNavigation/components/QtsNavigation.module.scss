@import '~@fluentui/react/dist/sass/References.scss';
@import '../../../styles/variables.scss';

.qtsNavigation {
  background-color: $qts-accent-color;
  padding: 0;
  margin: 0;
  width: 100%;
  font-family: $qts-font-family;
  box-shadow: $qts-shadow-light;
  position: relative;
  z-index: $qts-z-index-navigation;

  &.teams {
    font-family: $ms-font-family-fallbacks;
  }
}

.navigationContainer {
  max-width: $qts-breakpoint-desktop;
  margin: 0 auto;
  padding: 0 $qts-spacing-xl;
}

.navigationList {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  align-items: center;
}

.navigationItem {
  position: relative;

  &.hasDropdown {
    .navigationLink {
      display: flex;
      align-items: center;
      gap: $qts-spacing-xs;
    }
  }

  &.active {
    .navigationLink {
      background-color: rgba(255, 255, 255, 0.15);
      font-weight: $qts-medium-font-weight;
    }
  }
}

.navigationLink {
  display: block;
  color: $qts-text-color;
  text-decoration: none;
  padding: $qts-spacing-lg $qts-spacing-xl;
  font-size: $qts-font-size-medium;
  font-weight: $qts-normal-font-weight;
  transition: all $qts-transition-normal;
  border-radius: $qts-border-radius-md;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    text-decoration: none;
  }

  &:focus {
    outline: 2px solid $qts-text-color;
    outline-offset: 2px;
  }
}

.dropdownArrow {
  font-size: $qts-font-size-small;
  margin-left: $qts-spacing-xs;
  transition: transform $qts-transition-fast;
}

.dropdownMenu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: $qts-text-color;
  border-radius: $qts-border-radius-md;
  box-shadow: $qts-shadow-medium;
  list-style: none;
  margin: 0;
  padding: $qts-spacing-sm 0;
  min-width: 200px;
  z-index: $qts-z-index-modal;
}

.dropdownItem {
  margin: 0;
}

.dropdownLink {
  display: block;
  color: $qts-text-dark;
  text-decoration: none;
  padding: $qts-spacing-md $qts-spacing-lg;
  font-size: $qts-font-size-normal;
  transition: background-color $qts-transition-fast;

  &:hover {
    background-color: rgba(0, 77, 153, 0.1);
    text-decoration: none;
  }

  &:focus {
    outline: 2px solid $qts-primary-color;
    outline-offset: -2px;
  }
}

// Responsive design
@media (max-width: $qts-breakpoint-mobile) {
  .navigationContainer {
    padding: 0 $qts-spacing-md;
  }

  .navigationList {
    flex-direction: column;
    align-items: stretch;
  }

  .navigationItem {
    width: 100%;

    &.hasDropdown {
      .dropdownMenu {
        position: static;
        box-shadow: none;
        background-color: rgba(255, 255, 255, 0.1);
        margin-left: $qts-spacing-lg;
      }
    }
  }

  .navigationLink {
    padding: $qts-spacing-md $qts-spacing-lg;
    font-size: $qts-font-size-normal;
  }

  .dropdownLink {
    padding: $qts-spacing-sm $qts-spacing-lg;
    font-size: $qts-font-size-small;
    color: $qts-text-color;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}