import * as React from 'react';
import styles from './QtsNavigation.module.scss';
import type { IQtsNavigationProps } from './IQtsNavigationProps';

interface INavigationItem {
  title: string;
  url: string;
  isActive?: boolean;
  children?: INavigationItem[];
}

interface IQtsNavigationState {
  navigationItems: INavigationItem[];
  activeDropdown: string | undefined;
}

export default class QtsNavigation extends React.Component<IQtsNavigationProps, IQtsNavigationState> {

  constructor(props: IQtsNavigationProps) {
    super(props);

    this.state = {
      navigationItems: [
        { title: 'Home', url: '/', isActive: true },
        {
          title: 'Services',
          url: '/services',
          children: [
            { title: 'Cloud Solutions', url: '/services/cloud' },
            { title: 'Data Analytics', url: '/services/analytics' },
            { title: 'Consulting', url: '/services/consulting' }
          ]
        },
        {
          title: 'Solutions',
          url: '/solutions',
          children: [
            { title: 'Enterprise', url: '/solutions/enterprise' },
            { title: 'Small Business', url: '/solutions/small-business' }
          ]
        },
        { title: 'About', url: '/about' },
        { title: 'Contact', url: '/contact' }
      ],
      activeDropdown: undefined
    };
  }

  private handleDropdownToggle = (title: string): void => {
    this.setState({
      activeDropdown: this.state.activeDropdown === title ? undefined : title
    });
  }

  private handleDropdownClose = (): void => {
    this.setState({ activeDropdown: undefined });
  }

  public render(): React.ReactElement<IQtsNavigationProps> {
    const { hasTeamsContext } = this.props;
    const { navigationItems, activeDropdown } = this.state;

    return (
      <nav className={`${styles.qtsNavigation} ${hasTeamsContext ? styles.teams : ''}`}>
        <div className={styles.navigationContainer}>
          <ul className={styles.navigationList}>
            {navigationItems.map((item, index) => (
              <li
                key={index}
                className={`${styles.navigationItem} ${item.isActive ? styles.active : ''} ${item.children ? styles.hasDropdown : ''}`}
                onMouseLeave={this.handleDropdownClose}
              >
                <a
                  href={item.url}
                  className={styles.navigationLink}
                  onClick={item.children ? (e) => {
                    e.preventDefault();
                    this.handleDropdownToggle(item.title);
                  } : undefined}
                  onMouseEnter={item.children ? () => this.handleDropdownToggle(item.title) : undefined}
                >
                  {item.title}
                  {item.children && <span className={styles.dropdownArrow}>▼</span>}
                </a>

                {item.children && activeDropdown === item.title && (
                  <ul className={styles.dropdownMenu}>
                    {item.children.map((child, childIndex) => (
                      <li key={childIndex} className={styles.dropdownItem}>
                        <a href={child.url} className={styles.dropdownLink}>
                          {child.title}
                        </a>
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
          </ul>
        </div>
      </nav>
    );
  }
}
