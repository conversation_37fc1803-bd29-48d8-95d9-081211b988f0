@import '~@fluentui/react/dist/sass/References.scss';
@import '../../../styles/variables.scss';

.qtsHeader {
  background-color: $qts-primary-color;
  color: $qts-text-color;
  padding: 0;
  margin: 0;
  width: 100%;
  font-family: $qts-font-family;

  &.teams {
    font-family: $ms-font-family-fallbacks;
  }
}

.headerContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.logo {
  display: flex;
  align-items: center;
}

.logoLink {
  display: inline-block;
  text-decoration: none;
  transition: opacity $qts-transition-normal;

  &:hover {
    opacity: 0.9;
  }

  &:focus {
    outline: 2px solid $qts-text-color;
    outline-offset: 4px;
    border-radius: $qts-border-radius-sm;
  }
}

.logoImage {
  height: 40px;
  width: auto;
  max-width: 200px;
  display: block;
}

.navigation {
  display: flex;
  align-items: center;
}

.navList {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 30px;
}

.navItem {
  display: inline-block;
}

.navLink {
  color: $qts-text-color;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    text-decoration: underline;
    background-color: rgba(255, 255, 255, 0.1);
  }

  &:focus {
    outline: 2px solid $qts-text-color;
    outline-offset: 2px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .headerContainer {
    flex-direction: column;
    padding: 15px;
    gap: 15px;
  }

  .navList {
    gap: 20px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .logoImage {
    height: 32px;
    max-width: 150px;
  }

  .navLink {
    font-size: 14px;
    padding: 6px 10px;
  }
}