import * as React from 'react';
import styles from './QtsHeader.module.scss';
import type { IQtsHeaderProps } from './IQtsHeaderProps';

// Import logo images
// eslint-disable-next-line @typescript-eslint/no-var-requires
const qtsLogoLarge: string = require('../../../assets/images/qts-logo-large.png');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const qtsLogoMedium: string = require('../../../assets/images/qts-logo-medium.png');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const qtsLogoSmall: string = require('../../../assets/images/qts-logo-small.png');

export default class QtsHeader extends React.Component<IQtsHeaderProps> {
  public render(): React.ReactElement<IQtsHeaderProps> {
    const {
      hasTeamsContext
    } = this.props;

    return (
      <header className={`${styles.qtsHeader} ${hasTeamsContext ? styles.teams : ''}`}>
        <div className={styles.headerContainer}>
          <div className={styles.logo}>
            <a href="/" className={styles.logoLink} aria-label="QTS Home">
              <picture>
                <source media="(max-width: 480px)" srcSet={qtsLogoSmall} />
                <source media="(max-width: 768px)" srcSet={qtsLogoMedium} />
                <img
                  src={qtsLogoLarge}
                  alt="QTS Logo"
                  className={styles.logoImage}
                />
              </picture>
            </a>
          </div>
          <nav className={styles.navigation}>
            <ul className={styles.navList}>
              <li className={styles.navItem}>
                <a href="#" className={styles.navLink}>Home</a>
              </li>
              <li className={styles.navItem}>
                <a href="#" className={styles.navLink}>Services</a>
              </li>
              <li className={styles.navItem}>
                <a href="#" className={styles.navLink}>About</a>
              </li>
              <li className={styles.navItem}>
                <a href="#" className={styles.navLink}>Contact</a>
              </li>
            </ul>
          </nav>
        </div>
      </header>
    );
  }
}
