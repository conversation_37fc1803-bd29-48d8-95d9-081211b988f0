import * as React from 'react';
import styles from './QtsFooter.module.scss';
import type { IQtsFooterProps } from './IQtsFooterProps';

export default class QtsFooter extends React.Component<IQtsFooterProps> {
  public render(): React.ReactElement<IQtsFooterProps> {
    const {
      hasTeamsContext
    } = this.props;

    const currentYear = new Date().getFullYear();

    return (
      <footer className={`${styles.qtsFooter} ${hasTeamsContext ? styles.teams : ''}`}>
        <div className={styles.footerContainer}>
          <div className={styles.footerContent}>
            <div className={styles.copyrightSection}>
              <p className={styles.copyrightText}>
                &copy; {currentYear} QTS. All Rights Reserved.
              </p>
            </div>
            <nav className={styles.footerNavigation}>
              <ul className={styles.footerNavList}>
                <li className={styles.footerNavItem}>
                  <a href="#" className={styles.footerNavLink}>Privacy Policy</a>
                </li>
                <li className={styles.footerNavItem}>
                  <a href="#" className={styles.footerNavLink}>Terms of Service</a>
                </li>
                <li className={styles.footerNavItem}>
                  <a href="#" className={styles.footerNavLink}>Contact Us</a>
                </li>
                <li className={styles.footerNavItem}>
                  <a href="#" className={styles.footerNavLink}>Support</a>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </footer>
    );
  }
}
