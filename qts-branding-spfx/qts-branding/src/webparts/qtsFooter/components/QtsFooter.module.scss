@import '~@fluentui/react/dist/sass/References.scss';
@import '../../../styles/variables.scss';

.qtsFooter {
  background-color: $qts-secondary-color;
  color: $qts-text-color;
  padding: 0;
  margin: 0;
  width: 100%;
  font-family: $qts-font-family;
  margin-top: auto;

  &.teams {
    font-family: $ms-font-family-fallbacks;
  }
}

.footerContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.footerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.copyrightSection {
  flex: 1;
}

.copyrightText {
  margin: 0;
  font-size: 14px;
  color: $qts-text-color;
}

.footerNavigation {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.footerNavList {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 25px;
  flex-wrap: wrap;
}

.footerNavItem {
  display: inline-block;
}

.footerNavLink {
  color: $qts-text-color;
  text-decoration: none;
  font-size: 14px;
  font-weight: 400;
  padding: 4px 8px;
  border-radius: 3px;
  transition: all 0.3s ease;

  &:hover {
    text-decoration: underline;
    background-color: rgba(255, 255, 255, 0.1);
  }

  &:focus {
    outline: 2px solid $qts-text-color;
    outline-offset: 2px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .footerContent {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .footerNavigation {
    justify-content: center;
  }

  .footerNavList {
    gap: 15px;
    justify-content: center;
  }

  .copyrightText {
    font-size: 13px;
  }

  .footerNavLink {
    font-size: 13px;
    padding: 3px 6px;
  }
}