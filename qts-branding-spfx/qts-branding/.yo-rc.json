{"@microsoft/generator-sharepoint": {"whichFolder": "subdir", "solutionName": "qts-branding", "skipFeatureDeployment": true, "componentType": "webpart", "template": "react", "componentName": "QTSNavigation", "componentDescription": "Custom QTS Navigation for branding", "plusBeta": false, "isCreatingSolution": false, "nodeVersion": "22.14.0", "sdksVersions": {}, "version": "1.21.1", "libraryName": "qts-branding", "libraryId": "ee34bfd9-7545-42fd-a0c0-8a6744102311", "environment": "spo", "packageManager": "npm", "solutionShortDescription": "qts-branding description", "isDomainIsolated": false}}