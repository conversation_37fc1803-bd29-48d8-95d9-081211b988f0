# QTS Branding SharePoint Framework Solution

This SharePoint Framework (SPFx) solution provides custom branding components for QTS, including header, footer, and navigation web parts that implement the QTS brand identity.

## Components

### 1. QTS Header Web Part (`QtsHeader`)
- **Purpose**: Custom branded header with QTS logo and primary navigation
- **Features**:
  - **Official QTS Logo**: Responsive logo with multiple sizes (large, medium, small)
  - **Smart Image Loading**: Uses HTML5 `<picture>` element for optimal loading
  - QTS brand colors (#004d99 primary blue)
  - Responsive design with mobile-optimized logo sizing
  - Clean, professional layout
  - Accessibility features (focus states, ARIA labels, alt text)

### 2. QTS Footer Web Part (`QtsFooter`)
- **Purpose**: Custom branded footer with copyright and secondary navigation
- **Features**:
  - Darker QTS brand color (#003366)
  - Copyright information with dynamic year
  - Footer navigation links (Privacy Policy, Terms of Service, Contact Us, Support)
  - Responsive layout

### 3. QTS Navigation Web Part (`QtsNavigation`)
- **Purpose**: Advanced navigation with dropdown menus
- **Features**:
  - Multi-level navigation support
  - Hover and click dropdown functionality
  - Active state indicators
  - Mobile-responsive design
  - Smooth transitions and animations

## Brand Guidelines

### Colors
- **Primary Blue**: #004d99 (Headers, main branding)
- **Secondary Blue**: #003366 (Footer, darker elements)
- **Accent Blue**: #0066cc (Navigation, interactive elements)
- **Text White**: #ffffff (Text on dark backgrounds)
- **Text Dark**: #333333 (Text on light backgrounds)

### Typography
- **Font Family**: Arial, sans-serif
- **Font Weights**: 
  - Normal: 400
  - Medium: 500
  - Bold: 700

### Spacing
- Consistent spacing scale using SCSS variables
- Responsive padding and margins
- Mobile-first approach

## File Structure

```
src/
├── assets/
│   └── images/                 # QTS logo assets
│       ├── qts-logo-large.png  # 300px wide logo
│       ├── qts-logo-medium.png # 200px wide logo
│       ├── qts-logo-small.png  # 120px wide logo
│       └── qts-logo-icon.png   # 64x64px icon
├── styles/
│   └── variables.scss          # Global brand variables
├── webparts/
│   ├── qtsHeader/
│   │   ├── components/
│   │   │   ├── QtsHeader.tsx
│   │   │   ├── QtsHeader.module.scss
│   │   │   └── IQtsHeaderProps.ts
│   │   └── QtsHeaderWebPart.ts
│   ├── qtsFooter/
│   │   ├── components/
│   │   │   ├── QtsFooter.tsx
│   │   │   ├── QtsFooter.module.scss
│   │   │   └── IQtsFooterProps.ts
│   │   └── QtsFooterWebPart.ts
│   └── qtsNavigation/
│       ├── components/
│       │   ├── QtsNavigation.tsx
│       │   ├── QtsNavigation.module.scss
│       │   └── IQtsNavigationProps.ts
│       └── QtsNavigationWebPart.ts
```

## Installation and Deployment

### Prerequisites
- Node.js (LTS version)
- SharePoint Framework development environment
- Access to SharePoint Online tenant

### Development Setup
1. Clone or download the solution
2. Navigate to the solution directory:
   ```bash
   cd qts-branding
   ```
3. Install dependencies:
   ```bash
   npm install
   ```
4. Start development server:
   ```bash
   gulp serve
   ```

### Building for Production
1. Bundle the solution:
   ```bash
   gulp bundle --ship
   ```
2. Package the solution:
   ```bash
   gulp package-solution --ship
   ```
3. Upload the `.sppkg` file to your SharePoint App Catalog

### Deployment
1. Go to your SharePoint App Catalog
2. Upload the `qts-branding.sppkg` file from the `sharepoint/solution` folder
3. Deploy the solution to make it available across your tenant
4. Add the web parts to your SharePoint pages

## Usage

### Adding Web Parts to Pages
1. Edit a SharePoint page
2. Click the "+" icon to add a web part
3. Search for "QTS" to find the custom web parts:
   - QTS Header
   - QTS Footer  
   - QTS Navigation
4. Add the desired web parts to your page
5. Configure any properties as needed
6. Save and publish the page

### Recommended Page Layout
For a complete QTS branded page:
1. Add QTS Header at the top
2. Add QTS Navigation below the header
3. Add your content in the middle
4. Add QTS Footer at the bottom

## Customization

### Updating Brand Colors
Edit the `src/styles/variables.scss` file to modify brand colors:
```scss
$qts-primary-color: #004d99;      // Change primary color
$qts-secondary-color: #003366;    // Change secondary color
```

### Adding Navigation Items
Edit the `QtsNavigation.tsx` component to modify navigation structure:
```typescript
navigationItems: [
  { title: 'Home', url: '/', isActive: true },
  { title: 'New Section', url: '/new-section' }
]
```

### Responsive Breakpoints
Modify breakpoints in `variables.scss`:
```scss
$qts-breakpoint-mobile: 768px;
$qts-breakpoint-tablet: 1024px;
```

## Browser Support
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Internet Explorer 11 (with limitations)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Accessibility Features
- ARIA labels and roles
- Keyboard navigation support
- Focus indicators
- Screen reader compatibility
- High contrast mode support

## Support
For technical support or questions about this solution, please contact the QTS development team.

## Deployment Package
The solution has been successfully built and packaged. You can find the deployment package at:
```
sharepoint/solution/qts-branding.sppkg
```

This `.sppkg` file is ready to be uploaded to your SharePoint App Catalog for deployment.

## Quick Start Guide

### 1. Deploy to SharePoint
1. Go to your SharePoint Admin Center
2. Navigate to "More features" > "Apps" > "App Catalog"
3. Upload the `qts-branding.sppkg` file
4. Click "Deploy" when prompted
5. The solution will be available across your tenant

### 2. Add Web Parts to Pages
1. Edit any SharePoint page
2. Click the "+" icon to add a web part
3. Search for "QTS" to find:
   - **QTS Header** - Custom branded header
   - **QTS Footer** - Custom branded footer
   - **QTS Navigation** - Advanced navigation with dropdowns

### 3. Recommended Page Layout
For complete QTS branding:
1. Add QTS Header at the top of the page
2. Add QTS Navigation below the header
3. Add your content sections
4. Add QTS Footer at the bottom

## Version History
- **1.0.0**: Initial release with Header, Footer, and Navigation web parts
- Includes responsive design and accessibility features
- Full QTS brand implementation
- Production-ready SharePoint package (.sppkg) included
